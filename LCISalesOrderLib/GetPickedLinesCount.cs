/*
 * =====================================================
 * LCI Warehouse Analytics - Comprehensive Productivity & Workload Dashboard
 * =====================================================
 *
 * Purpose: Provides real-time warehouse productivity metrics and workload analysis,
 *          tracking both sales order fulfillment and job material allocation across
 *          individual users and overall operations.
 *
 * Parameters: None (automatically queries LCI company data)
 *
 * Returns:
 *   - result (System.Data.DataSet): Multi-table dataset with KPIs, user stats, and debug info
 *   - errorMessage (string): Empty if successful, detailed error message if failed
 *
 * Company: Hardcoded to 162250 (LCI)
 * Week Definition: Monday to Sunday business week
 *
 * ===== PRODUCTIVITY METRICS (Lines Picked/Completed) =====
 *   1. TodaysPickedLines - Total warehouse output today
 *   2. YesterdaysPickedLines - Total warehouse output yesterday
 *   3. ThisWeeksPickedLines - Total warehouse output this week
 *
 * ===== WORKLOAD METRICS (Lines Due/Required) =====
 *   4. TodaysDueLines - Work requiring immediate attention
 *   5. TomorrowsDueLines - Work due tomorrow
 *   6. ThisWeeksDueLines - Work due this week
 *   7. OverdueLines - Past due work requiring urgent attention
 *   8. OverduePercentage - Percentage of dated work that is overdue
 *
 * ===== DATASET TABLES =====
 *   1. "Statistics" - Executive dashboard with 8 key performance indicators
 *   2. "UserStatistics" - Individual productivity by warehouse user
 *   3. "Debug" - Technical analysis and troubleshooting data
 *
 * ===== ADVANCED COUNTING LOGIC =====
 *
 * PICKED LINES - What Counts as "1 Line Completed":
 *   • Sales Order Lines: Customer shipments (*-CUS transactions, full quantity shipped)
 *   • Kit Components: Each material allocated for completed kit orders (STK-MTL to jobs)
 *   • Job Materials: Production materials picked for manufacturing (STK-MTL, full quantity)
 *   • Quality Standard: Only 100% completed items count (no partial credit)
 *   • Anti-Duplication: Smart logic prevents double-counting across transaction types
 *
 * DUE LINES - What Counts as "1 Line Due":
 *   • Sales Order Lines: Open customer orders with NeedByDate in timeframe
 *   • Job Materials: Incomplete production materials with job ReqDueDate in timeframe
 *   • Active Work Only: Excludes completed, closed, or cancelled items
 *
 * USER TRACKING:
 *   • Source: PartTran.EntryPerson field from actual picking transactions
 *   • Metrics: Lines picked today/yesterday/this week per individual user
 *   • Purpose: Performance analysis, workload distribution, team management
 *
 * Note: Time periods overlap intentionally (yesterday included in week totals)
 *       All metrics reflect actual warehouse work, not just administrative processing
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = new DataSet("PickedLinesStatistics");  // Initialize result DataSet
errorMessage = "";                     // Initialize error message as empty (success)

try
{
    // Create statistics table
    DataTable statsTable = new DataTable("Statistics");
    statsTable.Columns.Add("Category", typeof(string));
    statsTable.Columns.Add("Count", typeof(decimal));
    statsTable.Columns.Add("Description", typeof(string));

    // Get date ranges for filtering - calculate all dates upfront to avoid LINQ translation issues
    DateTime today = DateTime.Today;
    DateTime yesterday = DateTime.Today.AddDays(-1);
    DateTime tomorrow = DateTime.Today.AddDays(1);

    // Fix week calculation - Monday = start of week (more business-appropriate)
    int daysFromMonday = ((int)DateTime.Today.DayOfWeek + 6) % 7; // Monday = 0, Tuesday = 1, etc.
    DateTime weekStart = DateTime.Today.AddDays(-daysFromMonday);  // Start of this week (Monday)
    DateTime weekEnd = weekStart.AddDays(7);  // End of this week (next Monday)

    DateTime dayAfterTomorrow = DateTime.Today.AddDays(2);
    DateTime monthStart = DateTime.Today.AddDays(-30);
    DateTime monthEnd = DateTime.Today.AddDays(30);

    // ===== PICKED LINES STATISTICS =====
    // Using PartTran table to track actual picking/shipping transactions (all *-CUS types)
    // *-CUS = Any transaction type ending in "-CUS" represents customer fulfillment
    // Each OrderNum/OrderLine combination is counted only once per day to prevent double counting
    // Only count lines where the total shipped quantity >= order line quantity (fully completed)

    // 1. Today's picked lines - count distinct order lines that were fully completed today
    // This includes both regular lines and kit component counting

    // First, get regular completed lines (non-kit)
    var todaysRegularLines = (from pt in Db.PartTran
                             join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                             where pt.Company == companyID &&
                                   pt.TranType.EndsWith("-CUS") &&
                                   pt.OrderNum > 0 &&
                                   pt.OrderLine > 0 &&
                                   pt.TranDate.HasValue &&
                                   pt.TranDate.Value >= today &&
                                   pt.TranDate.Value < tomorrow
                             group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty } into g
                             where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                             select new { g.Key.OrderNum, g.Key.OrderLine })
                            .Distinct()
                            .Count();

    // Second, get kit component lines (materials allocated to jobs today for completed order lines)
    var todaysKitComponents = (from pt in Db.PartTran
                              join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                              join jp in Db.JobProd on new { od.Company, od.OrderNum, od.OrderLine } equals new { jp.Company, jp.OrderNum, jp.OrderLine }
                              join jh in Db.JobHead on new { jp.Company, jp.JobNum } equals new { jh.Company, jh.JobNum }
                              join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                              join ptMtl in Db.PartTran on new { jm.Company, jm.JobNum, JobSeq = jm.MtlSeq } equals new { ptMtl.Company, ptMtl.JobNum, ptMtl.JobSeq }
                              where pt.Company == companyID &&
                                    pt.TranType.EndsWith("-CUS") &&
                                    pt.OrderNum > 0 &&
                                    pt.OrderLine > 0 &&
                                    pt.TranDate.HasValue &&
                                    pt.TranDate.Value >= today &&
                                    pt.TranDate.Value < tomorrow &&
                                    ptMtl.TranType == "STK-MTL" &&
                                    ptMtl.TranDate.HasValue &&
                                    ptMtl.TranDate.Value >= today &&
                                    ptMtl.TranDate.Value < tomorrow
                              group new { pt, ptMtl, jm } by new { pt.OrderNum, pt.OrderLine, od.OrderQty, jm.MtlSeq } into g
                              where g.Sum(x => x.pt.TranQty) >= g.Key.OrderQty // Order line is completed
                              select new { g.Key.OrderNum, g.Key.OrderLine, g.Key.MtlSeq })
                             .Distinct()
                             .Count();

    // Third, get standalone job material picks (materials picked for jobs today, regardless of order completion status)
    var todaysJobMaterialPicks = (from ptMtl in Db.PartTran
                                 join jm in Db.JobMtl on new { ptMtl.Company, ptMtl.JobNum, MtlSeq = ptMtl.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
                                 join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
                                 join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                 where ptMtl.Company == companyID &&
                                       ptMtl.TranType == "STK-MTL" &&
                                       !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                       ptMtl.TranDate.HasValue &&
                                       ptMtl.TranDate.Value >= today &&
                                       ptMtl.TranDate.Value < tomorrow &&
                                       jp.OrderNum > 0 &&
                                       jp.OrderLine > 0
                                 group ptMtl by new { ptMtl.JobNum, ptMtl.JobSeq, jm.RequiredQty } into g
                                 where g.Sum(pt => pt.TranQty) >= g.Key.RequiredQty // Material fully picked
                                 select new { g.Key.JobNum, g.Key.JobSeq })
                                .Distinct()
                                .Count();

    var todaysPickedLines = todaysRegularLines + todaysKitComponents + todaysJobMaterialPicks;

    // 2. Yesterday's picked lines - count distinct order lines that were fully completed yesterday
    // This includes both regular lines and kit component counting

    // First, get regular completed lines (non-kit)
    var yesterdaysRegularLines = (from pt in Db.PartTran
                                 join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                                 where pt.Company == companyID &&
                                       pt.TranType.EndsWith("-CUS") &&
                                       pt.OrderNum > 0 &&
                                       pt.OrderLine > 0 &&
                                       pt.TranDate.HasValue &&
                                       pt.TranDate.Value >= yesterday &&
                                       pt.TranDate.Value < today
                                 group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty } into g
                                 where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                                 select new { g.Key.OrderNum, g.Key.OrderLine })
                                .Distinct()
                                .Count();

    // Second, get kit component lines (materials allocated to jobs yesterday for completed order lines)
    var yesterdaysKitComponents = (from pt in Db.PartTran
                                  join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                                  join jp in Db.JobProd on new { od.Company, od.OrderNum, od.OrderLine } equals new { jp.Company, jp.OrderNum, jp.OrderLine }
                                  join jh in Db.JobHead on new { jp.Company, jp.JobNum } equals new { jh.Company, jh.JobNum }
                                  join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                                  join ptMtl in Db.PartTran on new { jm.Company, jm.JobNum, JobSeq = jm.MtlSeq } equals new { ptMtl.Company, ptMtl.JobNum, ptMtl.JobSeq }
                                  where pt.Company == companyID &&
                                        pt.TranType.EndsWith("-CUS") &&
                                        pt.OrderNum > 0 &&
                                        pt.OrderLine > 0 &&
                                        pt.TranDate.HasValue &&
                                        pt.TranDate.Value >= yesterday &&
                                        pt.TranDate.Value < today &&
                                        ptMtl.TranType == "STK-MTL" &&
                                        ptMtl.TranDate.HasValue &&
                                        ptMtl.TranDate.Value >= yesterday &&
                                        ptMtl.TranDate.Value < today
                                  group new { pt, ptMtl, jm } by new { pt.OrderNum, pt.OrderLine, od.OrderQty, jm.MtlSeq } into g
                                  where g.Sum(x => x.pt.TranQty) >= g.Key.OrderQty // Order line is completed
                                  select new { g.Key.OrderNum, g.Key.OrderLine, g.Key.MtlSeq })
                                 .Distinct()
                                 .Count();

    // Third, get standalone job material picks (materials picked for jobs yesterday, regardless of order completion status)
    var yesterdaysJobMaterialPicks = (from ptMtl in Db.PartTran
                                     join jm in Db.JobMtl on new { ptMtl.Company, ptMtl.JobNum, MtlSeq = ptMtl.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
                                     join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
                                     join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                     where ptMtl.Company == companyID &&
                                           ptMtl.TranType == "STK-MTL" &&
                                           !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                           ptMtl.TranDate.HasValue &&
                                           ptMtl.TranDate.Value >= yesterday &&
                                           ptMtl.TranDate.Value < today &&
                                           jp.OrderNum > 0 &&
                                           jp.OrderLine > 0
                                     group ptMtl by new { ptMtl.JobNum, ptMtl.JobSeq, jm.RequiredQty } into g
                                     where g.Sum(pt => pt.TranQty) >= g.Key.RequiredQty // Material fully picked
                                     select new { g.Key.JobNum, g.Key.JobSeq })
                                    .Distinct()
                                    .Count();

    var yesterdaysPickedLines = yesterdaysRegularLines + yesterdaysKitComponents + yesterdaysJobMaterialPicks;

    // 3. This week's picked lines - count distinct order lines that were fully completed this week
    // This includes both regular lines and kit component counting

    // First, get regular completed lines (non-kit)
    var thisWeeksRegularLines = (from pt in Db.PartTran
                                join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                                where pt.Company == companyID &&
                                      pt.TranType.EndsWith("-CUS") &&
                                      pt.OrderNum > 0 &&
                                      pt.OrderLine > 0 &&
                                      pt.TranDate.HasValue &&
                                      pt.TranDate.Value >= weekStart &&
                                      pt.TranDate.Value < weekEnd
                                group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty } into g
                                where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                                select new { g.Key.OrderNum, g.Key.OrderLine })
                               .Distinct()
                               .Count();

    // Second, get kit component lines (materials allocated to jobs this week for completed order lines)
    var thisWeeksKitComponents = (from pt in Db.PartTran
                                 join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                                 join jp in Db.JobProd on new { od.Company, od.OrderNum, od.OrderLine } equals new { jp.Company, jp.OrderNum, jp.OrderLine }
                                 join jh in Db.JobHead on new { jp.Company, jp.JobNum } equals new { jh.Company, jh.JobNum }
                                 join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                                 join ptMtl in Db.PartTran on new { jm.Company, jm.JobNum, JobSeq = jm.MtlSeq } equals new { ptMtl.Company, ptMtl.JobNum, ptMtl.JobSeq }
                                 where pt.Company == companyID &&
                                       pt.TranType.EndsWith("-CUS") &&
                                       pt.OrderNum > 0 &&
                                       pt.OrderLine > 0 &&
                                       pt.TranDate.HasValue &&
                                       pt.TranDate.Value >= weekStart &&
                                       pt.TranDate.Value < weekEnd &&
                                       ptMtl.TranType == "STK-MTL" &&
                                       ptMtl.TranDate.HasValue &&
                                       ptMtl.TranDate.Value >= weekStart &&
                                       ptMtl.TranDate.Value < weekEnd
                                 group new { pt, ptMtl, jm } by new { pt.OrderNum, pt.OrderLine, od.OrderQty, jm.MtlSeq } into g
                                 where g.Sum(x => x.pt.TranQty) >= g.Key.OrderQty // Order line is completed
                                 select new { g.Key.OrderNum, g.Key.OrderLine, g.Key.MtlSeq })
                                .Distinct()
                                .Count();

    // Third, get standalone job material picks (materials picked for jobs this week, regardless of order completion status)
    var thisWeeksJobMaterialPicks = (from ptMtl in Db.PartTran
                                    join jm in Db.JobMtl on new { ptMtl.Company, ptMtl.JobNum, MtlSeq = ptMtl.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
                                    join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
                                    join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                    where ptMtl.Company == companyID &&
                                          ptMtl.TranType == "STK-MTL" &&
                                          !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                          ptMtl.TranDate.HasValue &&
                                          ptMtl.TranDate.Value >= weekStart &&
                                          ptMtl.TranDate.Value < weekEnd &&
                                          jp.OrderNum > 0 &&
                                          jp.OrderLine > 0
                                    group ptMtl by new { ptMtl.JobNum, ptMtl.JobSeq, jm.RequiredQty } into g
                                    where g.Sum(pt => pt.TranQty) >= g.Key.RequiredQty // Material fully picked
                                    select new { g.Key.JobNum, g.Key.JobSeq })
                                   .Distinct()
                                   .Count();

    var thisWeeksPickedLines = thisWeeksRegularLines + thisWeeksKitComponents + thisWeeksJobMaterialPicks;

    // ===== DUE DATE STATISTICS =====

    // 4. Today's due lines - includes both order lines and job materials due today

    // Regular order lines due today
    var todaysOrderLinesDue = (from oh in Db.OrderHed
                              join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                              where oh.Company == companyID &&
                                    oh.OpenOrder == true &&
                                    od.OpenLine == true &&
                                    od.NeedByDate.HasValue &&
                                    od.NeedByDate.Value >= today &&
                                    od.NeedByDate.Value < tomorrow
                              select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                             .Distinct()
                             .Count();

    // Job materials due today (based on job due dates)
    var todaysJobMaterialsDue = (from jh in Db.JobHead
                                join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                                where jh.Company == companyID &&
                                      jh.JobClosed == false &&
                                      jp.OrderNum > 0 &&
                                      jp.OrderLine > 0 &&
                                      jh.ReqDueDate.HasValue &&
                                      jh.ReqDueDate.Value >= today &&
                                      jh.ReqDueDate.Value < tomorrow &&
                                      jm.IssuedQty < jm.RequiredQty // Material not fully issued yet
                                select new { jh.JobNum, jm.MtlSeq })
                               .Distinct()
                               .Count();

    var todaysDueLines = todaysOrderLinesDue + todaysJobMaterialsDue;

    // 5. Tomorrow's due lines - includes both order lines and job materials due tomorrow

    // Regular order lines due tomorrow
    var tomorrowsOrderLinesDue = (from oh in Db.OrderHed
                                 join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                                 where oh.Company == companyID &&
                                       oh.OpenOrder == true &&
                                       od.OpenLine == true &&
                                       od.NeedByDate.HasValue &&
                                       od.NeedByDate.Value >= tomorrow &&
                                       od.NeedByDate.Value < dayAfterTomorrow
                                 select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                                .Distinct()
                                .Count();

    // Job materials due tomorrow (based on job due dates)
    var tomorrowsJobMaterialsDue = (from jh in Db.JobHead
                                   join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                   join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                                   where jh.Company == companyID &&
                                         jh.JobClosed == false &&
                                         jp.OrderNum > 0 &&
                                         jp.OrderLine > 0 &&
                                         jh.ReqDueDate.HasValue &&
                                         jh.ReqDueDate.Value >= tomorrow &&
                                         jh.ReqDueDate.Value < dayAfterTomorrow &&
                                         jm.IssuedQty < jm.RequiredQty // Material not fully issued yet
                                   select new { jh.JobNum, jm.MtlSeq })
                                  .Distinct()
                                  .Count();

    var tomorrowsDueLines = tomorrowsOrderLinesDue + tomorrowsJobMaterialsDue;

    // 6. This week's due lines - includes both order lines and job materials due this week

    // Regular order lines due this week
    var thisWeeksOrderLinesDue = (from oh in Db.OrderHed
                                 join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                                 where oh.Company == companyID &&
                                       oh.OpenOrder == true &&
                                       od.OpenLine == true &&
                                       od.NeedByDate.HasValue &&
                                       od.NeedByDate.Value >= weekStart &&
                                       od.NeedByDate.Value < weekEnd
                                 select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                                .Distinct()
                                .Count();

    // Job materials due this week (based on job due dates)
    var thisWeeksJobMaterialsDue = (from jh in Db.JobHead
                                   join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                   join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                                   where jh.Company == companyID &&
                                         jh.JobClosed == false &&
                                         jp.OrderNum > 0 &&
                                         jp.OrderLine > 0 &&
                                         jh.ReqDueDate.HasValue &&
                                         jh.ReqDueDate.Value >= weekStart &&
                                         jh.ReqDueDate.Value < weekEnd &&
                                         jm.IssuedQty < jm.RequiredQty // Material not fully issued yet
                                   select new { jh.JobNum, jm.MtlSeq })
                                  .Distinct()
                                  .Count();

    var thisWeeksDueLines = thisWeeksOrderLinesDue + thisWeeksJobMaterialsDue;

    // ===== OVERDUE STATISTICS =====

    // Calculate overdue lines (due before today) - includes both order lines and job materials

    // Regular overdue order lines
    var overdueOrderLines = (from oh in Db.OrderHed
                            join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                            where oh.Company == companyID &&
                                  oh.OpenOrder == true &&
                                  od.OpenLine == true &&
                                  od.NeedByDate.HasValue &&
                                  od.NeedByDate.Value < today
                            select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                           .Distinct()
                           .Count();

    // Overdue job materials (based on job due dates)
    var overdueJobMaterials = (from jh in Db.JobHead
                              join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                              join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                              where jh.Company == companyID &&
                                    jh.JobClosed == false &&
                                    jp.OrderNum > 0 &&
                                    jp.OrderLine > 0 &&
                                    jh.ReqDueDate.HasValue &&
                                    jh.ReqDueDate.Value < today &&
                                    jm.IssuedQty < jm.RequiredQty // Material not fully issued yet
                              select new { jh.JobNum, jm.MtlSeq })
                             .Distinct()
                             .Count();

    var overdueDueLines = overdueOrderLines + overdueJobMaterials;

    // Calculate percentage of overdue lines (overdue / total lines with dates)
    // Total must include both order lines AND job materials to match overdue calculation

    // Total order lines with dates
    var totalOrderLinesWithDates = (from oh in Db.OrderHed
                                   join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                                   where oh.Company == companyID &&
                                         oh.OpenOrder == true &&
                                         od.OpenLine == true &&
                                         od.NeedByDate.HasValue
                                   select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                                  .Distinct()
                                  .Count();

    // Total job materials with dates
    var totalJobMaterialsWithDates = (from jh in Db.JobHead
                                     join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                     join jm in Db.JobMtl on new { jh.Company, jh.JobNum } equals new { jm.Company, jm.JobNum }
                                     where jh.Company == companyID &&
                                           jh.JobClosed == false &&
                                           jp.OrderNum > 0 &&
                                           jp.OrderLine > 0 &&
                                           jh.ReqDueDate.HasValue &&
                                           jm.IssuedQty < jm.RequiredQty // Only incomplete materials
                                     select new { jh.JobNum, jm.MtlSeq })
                                    .Distinct()
                                    .Count();

    var totalLinesWithDates = totalOrderLinesWithDates + totalJobMaterialsWithDates;
    decimal overduePercentage = totalLinesWithDates > 0 ? (decimal)overdueDueLines / totalLinesWithDates : 0;

    // Add statistics to table
    statsTable.Rows.Add("TodaysPickedLines", todaysPickedLines, "Lines fully completed today (shipped qty >= order qty)");
    statsTable.Rows.Add("YesterdaysPickedLines", yesterdaysPickedLines, "Lines fully completed yesterday (shipped qty >= order qty)");
    statsTable.Rows.Add("ThisWeeksPickedLines", thisWeeksPickedLines, "Lines fully completed this week (shipped qty >= order qty)");
    statsTable.Rows.Add("TodaysDueLines", todaysDueLines, "Open lines due today");
    statsTable.Rows.Add("TomorrowsDueLines", tomorrowsDueLines, "Open lines due tomorrow");
    statsTable.Rows.Add("ThisWeeksDueLines", thisWeeksDueLines, "Open lines due this week (includes today/tomorrow)");
    statsTable.Rows.Add("OverdueLines", overdueDueLines, "Open lines overdue (due before today)");
    statsTable.Rows.Add("OverduePercentage", Math.Round(overduePercentage, 1), "Percentage of open lines with dates that are overdue");

    // Add statistics table to result DataSet
    result.Tables.Add(statsTable);

    // ===== PER-USER BREAKDOWN TABLE =====
    // Create per-user breakdown table showing lines picked by each user
    DataTable userStatsTable = new DataTable("UserStatistics");
    userStatsTable.Columns.Add("UserID", typeof(string));
    userStatsTable.Columns.Add("UserName", typeof(string));
    userStatsTable.Columns.Add("TodaysPickedLines", typeof(int));
    userStatsTable.Columns.Add("YesterdaysPickedLines", typeof(int));
    userStatsTable.Columns.Add("ThisWeeksPickedLines", typeof(int));

    // Get user statistics for today - includes regular lines, kit components, and job materials

    // Regular order lines by user (today) - use EntryPerson directly as username
    var todayRegularUserStats = (from pt in Db.PartTran
                                join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                                where pt.Company == companyID &&
                                      pt.TranType.EndsWith("-CUS") &&
                                      pt.OrderNum > 0 &&
                                      pt.OrderLine > 0 &&
                                      pt.TranDate.HasValue &&
                                      pt.TranDate.Value >= today &&
                                      pt.TranDate.Value < tomorrow &&
                                      !string.IsNullOrEmpty(pt.EntryPerson)
                                group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty, pt.EntryPerson } into g
                                where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                                group g by g.Key.EntryPerson into userGroup
                                select new {
                                    UserID = userGroup.Key,
                                    UserName = userGroup.Key, // Use EntryPerson as display name
                                    Count = userGroup.Select(g => new { g.Key.OrderNum, g.Key.OrderLine }).Distinct().Count()
                                }).ToList();

    // Job material picks by user (today) - use EntryPerson directly as username
    var todayJobMaterialUserStats = (from ptMtl in Db.PartTran
                                    join jm in Db.JobMtl on new { ptMtl.Company, ptMtl.JobNum, MtlSeq = ptMtl.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
                                    join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
                                    join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                    where ptMtl.Company == companyID &&
                                          ptMtl.TranType == "STK-MTL" &&
                                          !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                          ptMtl.TranDate.HasValue &&
                                          ptMtl.TranDate.Value >= today &&
                                          ptMtl.TranDate.Value < tomorrow &&
                                          jp.OrderNum > 0 &&
                                          jp.OrderLine > 0 &&
                                          !string.IsNullOrEmpty(ptMtl.EntryPerson)
                                    group ptMtl by new { ptMtl.JobNum, ptMtl.JobSeq, jm.RequiredQty, ptMtl.EntryPerson } into g
                                    where g.Sum(pt => pt.TranQty) >= g.Key.RequiredQty
                                    group g by g.Key.EntryPerson into userGroup
                                    select new {
                                        UserID = userGroup.Key,
                                        UserName = userGroup.Key, // Use EntryPerson as display name
                                        Count = userGroup.Select(g => new { g.Key.JobNum, g.Key.JobSeq }).Distinct().Count()
                                    }).ToList();

    // Combine regular and job material stats for today
    var todayUserStats = todayRegularUserStats.Concat(todayJobMaterialUserStats)
                        .GroupBy(u => u.UserID)
                        .Select(g => new {
                            UserID = g.Key,
                            UserName = g.First().UserName,
                            Count = g.Sum(u => u.Count)
                        })
                        .ToList();

    // Get user statistics for yesterday - includes regular lines, kit components, and job materials

    // Regular order lines by user (yesterday) - use EntryPerson directly as username
    var yesterdayRegularUserStats = (from pt in Db.PartTran
                                    join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                                    where pt.Company == companyID &&
                                          pt.TranType.EndsWith("-CUS") &&
                                          pt.OrderNum > 0 &&
                                          pt.OrderLine > 0 &&
                                          pt.TranDate.HasValue &&
                                          pt.TranDate.Value >= yesterday &&
                                          pt.TranDate.Value < today &&
                                          !string.IsNullOrEmpty(pt.EntryPerson)
                                    group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty, pt.EntryPerson } into g
                                    where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                                    group g by g.Key.EntryPerson into userGroup
                                    select new {
                                        UserID = userGroup.Key,
                                        UserName = userGroup.Key, // Use EntryPerson as display name
                                        Count = userGroup.Select(g => new { g.Key.OrderNum, g.Key.OrderLine }).Distinct().Count()
                                    }).ToList();

    // Job material picks by user (yesterday) - use EntryPerson directly as username
    var yesterdayJobMaterialUserStats = (from ptMtl in Db.PartTran
                                        join jm in Db.JobMtl on new { ptMtl.Company, ptMtl.JobNum, MtlSeq = ptMtl.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
                                        join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
                                        join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                        where ptMtl.Company == companyID &&
                                              ptMtl.TranType == "STK-MTL" &&
                                              !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                              ptMtl.TranDate.HasValue &&
                                              ptMtl.TranDate.Value >= yesterday &&
                                              ptMtl.TranDate.Value < today &&
                                              jp.OrderNum > 0 &&
                                              jp.OrderLine > 0 &&
                                              !string.IsNullOrEmpty(ptMtl.EntryPerson)
                                        group ptMtl by new { ptMtl.JobNum, ptMtl.JobSeq, jm.RequiredQty, ptMtl.EntryPerson } into g
                                        where g.Sum(pt => pt.TranQty) >= g.Key.RequiredQty
                                        group g by g.Key.EntryPerson into userGroup
                                        select new {
                                            UserID = userGroup.Key,
                                            UserName = userGroup.Key, // Use EntryPerson as display name
                                            Count = userGroup.Select(g => new { g.Key.JobNum, g.Key.JobSeq }).Distinct().Count()
                                        }).ToList();

    // Combine regular and job material stats for yesterday
    var yesterdayUserStats = yesterdayRegularUserStats.Concat(yesterdayJobMaterialUserStats)
                            .GroupBy(u => u.UserID)
                            .Select(g => new {
                                UserID = g.Key,
                                UserName = g.First().UserName,
                                Count = g.Sum(u => u.Count)
                            })
                            .ToList();

    // Get user statistics for this week - includes regular lines, kit components, and job materials

    // Regular order lines by user (this week) - use EntryPerson directly as username
    var weekRegularUserStats = (from pt in Db.PartTran
                               join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                               where pt.Company == companyID &&
                                     pt.TranType.EndsWith("-CUS") &&
                                     pt.OrderNum > 0 &&
                                     pt.OrderLine > 0 &&
                                     pt.TranDate.HasValue &&
                                     pt.TranDate.Value >= weekStart &&
                                     pt.TranDate.Value < weekEnd &&
                                     !string.IsNullOrEmpty(pt.EntryPerson)
                               group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty, pt.EntryPerson } into g
                               where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                               group g by g.Key.EntryPerson into userGroup
                               select new {
                                   UserID = userGroup.Key,
                                   UserName = userGroup.Key, // Use EntryPerson as display name
                                   Count = userGroup.Select(g => new { g.Key.OrderNum, g.Key.OrderLine }).Distinct().Count()
                               }).ToList();

    // Job material picks by user (this week) - use EntryPerson directly as username
    var weekJobMaterialUserStats = (from ptMtl in Db.PartTran
                                   join jm in Db.JobMtl on new { ptMtl.Company, ptMtl.JobNum, MtlSeq = ptMtl.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
                                   join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
                                   join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                   where ptMtl.Company == companyID &&
                                         ptMtl.TranType == "STK-MTL" &&
                                         !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                         ptMtl.TranDate.HasValue &&
                                         ptMtl.TranDate.Value >= weekStart &&
                                         ptMtl.TranDate.Value < weekEnd &&
                                         jp.OrderNum > 0 &&
                                         jp.OrderLine > 0 &&
                                         !string.IsNullOrEmpty(ptMtl.EntryPerson)
                                   group ptMtl by new { ptMtl.JobNum, ptMtl.JobSeq, jm.RequiredQty, ptMtl.EntryPerson } into g
                                   where g.Sum(pt => pt.TranQty) >= g.Key.RequiredQty
                                   group g by g.Key.EntryPerson into userGroup
                                   select new {
                                       UserID = userGroup.Key,
                                       UserName = userGroup.Key, // Use EntryPerson as display name
                                       Count = userGroup.Select(g => new { g.Key.JobNum, g.Key.JobSeq }).Distinct().Count()
                                   }).ToList();

    // Combine regular and job material stats for this week
    var weekUserStats = weekRegularUserStats.Concat(weekJobMaterialUserStats)
                       .GroupBy(u => u.UserID)
                       .Select(g => new {
                           UserID = g.Key,
                           UserName = g.First().UserName,
                           Count = g.Sum(u => u.Count)
                       })
                       .ToList();

    // Get all unique users from all time periods
    var allUsers = todayUserStats.Select(u => u.UserID)
                  .Union(yesterdayUserStats.Select(u => u.UserID))
                  .Union(weekUserStats.Select(u => u.UserID))
                  .Distinct()
                  .ToList();

    // Populate user statistics table with proper employee names
    foreach (var userID in allUsers)
    {
        var todayUser = todayUserStats.FirstOrDefault(u => u.UserID == userID);
        var yesterdayUser = yesterdayUserStats.FirstOrDefault(u => u.UserID == userID);
        var weekUser = weekUserStats.FirstOrDefault(u => u.UserID == userID);

        var todayCount = todayUser?.Count ?? 0;
        var yesterdayCount = yesterdayUser?.Count ?? 0;
        var weekCount = weekUser?.Count ?? 0;

        // Use the employee name from any available period, fallback to userID if no name found
        var userName = todayUser?.UserName ?? yesterdayUser?.UserName ?? weekUser?.UserName ?? userID;

        userStatsTable.Rows.Add(userID, userName, todayCount, yesterdayCount, weekCount);
    }

    // Add user statistics table to result DataSet
    result.Tables.Add(userStatsTable);

    // ===== DEBUG TABLE =====
    // Essential debugging for user/employee analysis
    DataTable debugTable = new DataTable("Debug");
    debugTable.Columns.Add("DebugCategory", typeof(string));
    debugTable.Columns.Add("DebugValue", typeof(string));
    debugTable.Columns.Add("DebugDescription", typeof(string));

    // Essential summary
    debugTable.Rows.Add("Summary", $"Today: {todaysPickedLines} picked, {todaysDueLines} due", "Today's key metrics");
    debugTable.Rows.Add("Summary", $"Yesterday: {yesterdaysPickedLines} picked, {tomorrowsDueLines} due tomorrow", "Yesterday and tomorrow metrics");
    debugTable.Rows.Add("Summary", $"Week: {thisWeeksPickedLines} picked, {thisWeeksDueLines} due", "Weekly metrics");

    // Calculate variables needed for later calculations
    var openOrderDtl = Db.OrderDtl.Where(od => od.Company == companyID && od.OpenLine == true).Count();
    var orderDtlWithNeedBy = Db.OrderDtl.Where(od => od.Company == companyID && od.OpenLine == true && od.NeedByDate.HasValue).Count();
    var ordersWithNullDates = Db.OrderDtl.Where(od => od.Company == companyID && od.OpenLine == true && !od.NeedByDate.HasValue).Count();
    var overdueOrders = (from oh in Db.OrderHed
                        join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                        where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                              od.NeedByDate.HasValue && od.NeedByDate.Value < today
                        select new { oh.OrderNum, od.OrderLine })
                        .Distinct().Count();



    // ===== USER/EMPLOYEE ANALYSIS =====
    // Check EntryPerson values and see if we can resolve to employee information

    var entryPersonSamples = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType.EndsWith("-CUS") &&
                    !string.IsNullOrEmpty(pt.EntryPerson) &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= yesterday)
        .Select(pt => pt.EntryPerson)
        .Distinct()
        .Take(10)
        .ToList();

    debugTable.Rows.Add("UserAnalysis", entryPersonSamples.Count.ToString(), "Distinct EntryPerson values found in recent transactions");

    for (int i = 0; i < Math.Min(5, entryPersonSamples.Count); i++)
    {
        var entryPerson = entryPersonSamples[i];
        debugTable.Rows.Add("EntryPersonSample", entryPerson, $"Sample EntryPerson value #{i + 1}");
    }

    // Try to find employee information by checking if EntryPerson matches EmpID
    try
    {
        var employeeMatches = (from pt in Db.PartTran
                              join emp in Db.EmpBasic on pt.EntryPerson equals emp.EmpID
                              where pt.Company == companyID &&
                                    pt.TranType.EndsWith("-CUS") &&
                                    !string.IsNullOrEmpty(pt.EntryPerson) &&
                                    pt.TranDate.HasValue &&
                                    pt.TranDate.Value >= yesterday
                              select new { pt.EntryPerson, emp.Name })
                              .Distinct()
                              .Take(5)
                              .ToList();

        debugTable.Rows.Add("UserAnalysis", employeeMatches.Count.ToString(), "EntryPerson values that match EmpBasic.EmpID");

        for (int i = 0; i < employeeMatches.Count; i++)
        {
            var match = employeeMatches[i];
            debugTable.Rows.Add("EmployeeMatch", $"{match.EntryPerson} = {match.Name}", $"Employee match #{i + 1}");
        }

        // Add debug info about EmpBasic records to understand the mapping
        var empBasicSamples = Db.EmpBasic
            .Where(emp => emp.Company == companyID)
            .Select(emp => new { emp.EmpID, emp.Name })
            .Take(10)
            .ToList();

        debugTable.Rows.Add("EmpBasicAnalysis", empBasicSamples.Count.ToString(), "Sample EmpBasic records found");

        for (int i = 0; i < Math.Min(5, empBasicSamples.Count); i++)
        {
            var emp = empBasicSamples[i];
            debugTable.Rows.Add("EmpBasicSample", $"{emp.EmpID} = {emp.Name}", $"EmpBasic record #{i + 1}");
        }

        // Try alternative matching approaches
        // Check if EntryPerson might be a substring of EmpID or Name
        var partialMatches = (from pt in Db.PartTran
                             join emp in Db.EmpBasic on true equals true
                             where pt.Company == companyID &&
                                   pt.TranType.EndsWith("-CUS") &&
                                   !string.IsNullOrEmpty(pt.EntryPerson) &&
                                   pt.TranDate.HasValue &&
                                   pt.TranDate.Value >= yesterday &&
                                   (emp.EmpID.Contains(pt.EntryPerson) ||
                                    emp.Name.ToLower().Contains(pt.EntryPerson.ToLower()) ||
                                    pt.EntryPerson.Contains(emp.EmpID))
                             select new { pt.EntryPerson, emp.EmpID, emp.Name })
                             .Distinct()
                             .Take(10)
                             .ToList();

        debugTable.Rows.Add("PartialMatches", partialMatches.Count.ToString(), "Partial matches between EntryPerson and EmpBasic");

        for (int i = 0; i < Math.Min(5, partialMatches.Count); i++)
        {
            var match = partialMatches[i];
            debugTable.Rows.Add("PartialMatch", $"{match.EntryPerson} ~ {match.EmpID} ({match.Name})", $"Partial match #{i + 1}");
        }
    }
    catch (Exception ex)
    {
        debugTable.Rows.Add("UserAnalysis", "Error", $"Employee lookup failed: {ex.Message}");
    }

    // Check if there are any patterns in EntryPerson that might indicate employee IDs
    var entryPersonPatterns = entryPersonSamples
        .GroupBy(ep => ep.Length)
        .Select(g => new { Length = g.Key, Count = g.Count(), Examples = string.Join(", ", g.Take(3)) })
        .ToList();

    foreach (var pattern in entryPersonPatterns)
    {
        debugTable.Rows.Add("EntryPersonPattern",
            $"Length {pattern.Length}: {pattern.Count} entries",
            $"Examples: {pattern.Examples}");
    }

    // Check EntryPerson values for STK-MTL transactions specifically
    var stkMtlEntryPersons = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType == "STK-MTL" &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= yesterday)
        .Select(pt => pt.EntryPerson)
        .Where(ep => !string.IsNullOrEmpty(ep))
        .Distinct()
        .Take(10)
        .ToList();

    debugTable.Rows.Add("STK-MTL-Users", stkMtlEntryPersons.Count.ToString(), "Distinct EntryPerson values in STK-MTL transactions");

    for (int i = 0; i < Math.Min(5, stkMtlEntryPersons.Count); i++)
    {
        var entryPerson = stkMtlEntryPersons[i];
        debugTable.Rows.Add("STK-MTL-Sample", entryPerson, $"STK-MTL EntryPerson #{i + 1}");
    }

    // Check how many STK-MTL transactions have null/empty EntryPerson
    var stkMtlWithoutUser = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType == "STK-MTL" &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= yesterday &&
                    (string.IsNullOrEmpty(pt.EntryPerson)))
        .Count();

    debugTable.Rows.Add("STK-MTL-NoUser", stkMtlWithoutUser.ToString(), "STK-MTL transactions with null/empty EntryPerson");

    // ===== KIT ANALYSIS =====
    // Check for kit-related information to understand kit structure

    // Look for KIT-CUS transactions to understand kit handling
    var kitCusTransactions = Db.PartTran
        .Where(pt => pt.Company == companyID && pt.TranType == "KIT-CUS" && pt.OrderNum > 0)
        .Take(10)
        .Select(pt => new { pt.OrderNum, pt.OrderLine, pt.PartNum, pt.TranQty, pt.TranDate })
        .ToList();

    debugTable.Rows.Add("KitAnalysis", kitCusTransactions.Count.ToString(), "Sample KIT-CUS transactions found");

    for (int i = 0; i < Math.Min(5, kitCusTransactions.Count); i++)
    {
        var kit = kitCusTransactions[i];
        debugTable.Rows.Add("KitSample",
            $"Order:{kit.OrderNum} Line:{kit.OrderLine} Part:{kit.PartNum} Qty:{kit.TranQty} Date:{kit.TranDate?.ToString("yyyy-MM-dd")}",
            $"Sample KIT-CUS transaction #{i + 1}");
    }

    // Check for parts that might be kits by looking at Part table
    var potentialKitParts = Db.Part
        .Where(p => p.Company == companyID && (p.TypeCode == "K" || p.PartNum.Contains("KIT")))
        .Take(10)
        .Select(p => new { p.PartNum, p.TypeCode, p.PartDescription })
        .ToList();

    debugTable.Rows.Add("KitAnalysis", potentialKitParts.Count.ToString(), "Potential kit parts found (TypeCode='K' or contains 'KIT')");

    for (int i = 0; i < Math.Min(3, potentialKitParts.Count); i++)
    {
        var kit = potentialKitParts[i];
        debugTable.Rows.Add("KitParts",
            $"Part:{kit.PartNum} Type:{kit.TypeCode} Desc:{kit.PartDescription}",
            $"Potential kit part #{i + 1}");
    }

    // Check for any tables that might contain kit component information
    try
    {
        // Try to find PartMtl records (Bill of Materials) which might show kit components
        var bomRecords = Db.PartMtl
            .Where(pm => pm.Company == companyID)
            .Take(5)
            .Select(pm => new { pm.PartNum, pm.MtlPartNum, pm.QtyPer })
            .ToList();

        debugTable.Rows.Add("KitAnalysis", bomRecords.Count.ToString(), "PartMtl (BOM) records found - may indicate kit components");

        for (int i = 0; i < Math.Min(3, bomRecords.Count); i++)
        {
            var bom = bomRecords[i];
            debugTable.Rows.Add("BOMSample",
                $"Parent:{bom.PartNum} Component:{bom.MtlPartNum} QtyPer:{bom.QtyPer}",
                $"Sample BOM record #{i + 1}");
        }
    }
    catch
    {
        debugTable.Rows.Add("KitAnalysis", "0", "PartMtl table not accessible or doesn't exist");
    }

    // ===== JOB MATERIAL ANALYSIS =====
    // Check for job material allocation to understand kit component tracking

    try
    {
        // Check for jobs associated with orders through JobProd
        var jobsWithOrders = (from jp in Db.JobProd
                             join jh in Db.JobHead on new { jp.Company, jp.JobNum } equals new { jh.Company, jh.JobNum }
                             where jp.Company == companyID && jp.OrderNum > 0 && jp.OrderLine > 0
                             select new { jh.JobNum, jp.OrderNum, jp.OrderLine, jh.PartNum })
                             .Take(10)
                             .ToList();

        debugTable.Rows.Add("JobAnalysis", jobsWithOrders.Count.ToString(), "Jobs associated with sales orders found");

        for (int i = 0; i < Math.Min(3, jobsWithOrders.Count); i++)
        {
            var job = jobsWithOrders[i];
            debugTable.Rows.Add("JobSample",
                $"Job:{job.JobNum} Order:{job.OrderNum} Line:{job.OrderLine} Part:{job.PartNum}",
                $"Sample job with order association #{i + 1}");
        }

        // Check for job materials
        var jobMaterials = Db.JobMtl
            .Where(jm => jm.Company == companyID)
            .Take(10)
            .Select(jm => new { jm.JobNum, jm.MtlSeq, jm.PartNum, jm.RequiredQty, jm.IssuedQty })
            .ToList();

        debugTable.Rows.Add("JobAnalysis", jobMaterials.Count.ToString(), "JobMtl records found");

        for (int i = 0; i < Math.Min(3, jobMaterials.Count); i++)
        {
            var mtl = jobMaterials[i];
            debugTable.Rows.Add("JobMtlSample",
                $"Job:{mtl.JobNum} Seq:{mtl.MtlSeq} Part:{mtl.PartNum} Req:{mtl.RequiredQty} Issued:{mtl.IssuedQty}",
                $"Sample job material #{i + 1}");
        }

        // Check for STK-MTL transactions (material allocation to jobs)
        var stkMtlTransactions = Db.PartTran
            .Where(pt => pt.Company == companyID && pt.TranType == "STK-MTL" && !string.IsNullOrEmpty(pt.JobNum))
            .Take(10)
            .Select(pt => new { pt.JobNum, pt.JobSeq, pt.PartNum, pt.TranQty, pt.TranDate })
            .ToList();

        debugTable.Rows.Add("JobAnalysis", stkMtlTransactions.Count.ToString(), "STK-MTL transactions (material to job) found");

        for (int i = 0; i < Math.Min(3, stkMtlTransactions.Count); i++)
        {
            var trans = stkMtlTransactions[i];
            debugTable.Rows.Add("STK-MTLSample",
                $"Job:{trans.JobNum} Seq:{trans.JobSeq} Part:{trans.PartNum} Qty:{trans.TranQty} Date:{trans.TranDate?.ToString("yyyy-MM-dd")}",
                $"Sample STK-MTL transaction #{i + 1}");
        }
    }
    catch (Exception ex)
    {
        debugTable.Rows.Add("JobAnalysis", "Error", $"Job analysis failed: {ex.Message}");
    }

    // Additional insights
    debugTable.Rows.Add("DueDateInsights", $"Total Open Lines: {openOrderDtl}", "Total open order lines");
    debugTable.Rows.Add("DueDateInsights", $"Lines with NeedByDate: {orderDtlWithNeedBy}", "Lines that have due dates");
    debugTable.Rows.Add("DueDateInsights", $"Lines without NeedByDate: {ordersWithNullDates}", "Lines missing due dates");
    debugTable.Rows.Add("DueDateInsights", $"Overdue Percentage: {(overdueOrders * 1.0 / orderDtlWithNeedBy):F1}", "Percentage of dated orders that are overdue (as decimal)");

    // Sample overdue orders to understand the pattern
    var sampleOverdueOrders = (from oh in Db.OrderHed
                              join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                              where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                                    od.NeedByDate.HasValue && od.NeedByDate.Value < today
                              orderby od.NeedByDate descending
                              select new { oh.OrderNum, od.OrderLine, od.NeedByDate, od.OrderQty })
                              .Take(5).ToList();

    for (int i = 0; i < sampleOverdueOrders.Count; i++)
    {
        var order = sampleOverdueOrders[i];
        int daysOverdue = (today - order.NeedByDate.Value).Days;
        debugTable.Rows.Add("OverdueSample",
            $"Order:{order.OrderNum} Line:{order.OrderLine} Due:{order.NeedByDate?.ToString("yyyy-MM-dd")} ({daysOverdue} days overdue)",
            $"Sample overdue order #{i + 1}");
    }

    // ===== TRANSACTION ANALYSIS =====
    // Check transaction counts by date to verify the new *-CUS approach

    // Count all customer transactions (*-CUS) by date with distinct OrderNum/OrderLine to prevent double counting
    var todayAllCustomerTrans = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType.EndsWith("-CUS") &&
                    pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= today && pt.TranDate.Value < tomorrow)
        .Select(pt => new { OrderNum = pt.OrderNum, OrderLine = pt.OrderLine })
        .Distinct().Count();

    var yesterdayAllCustomerTrans = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType.EndsWith("-CUS") &&
                    pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= yesterday && pt.TranDate.Value < today)
        .Select(pt => new { OrderNum = pt.OrderNum, OrderLine = pt.OrderLine })
        .Distinct().Count();

    debugTable.Rows.Add("TransAnalysis", $"Today All *-CUS Trans (Any Qty): {todayAllCustomerTrans}", "All *-CUS transaction types today (any quantity)");
    debugTable.Rows.Add("TransAnalysis", $"Yesterday All *-CUS Trans (Any Qty): {yesterdayAllCustomerTrans}", "All *-CUS transaction types yesterday (any quantity)");

    // Add detailed debugging for today's transactions
    var todayAllTransactions = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= today &&
                    pt.TranDate.Value < tomorrow)
        .GroupBy(pt => pt.TranType)
        .Select(g => new { TranType = g.Key, Count = g.Count() })
        .OrderByDescending(g => g.Count)
        .Take(10)
        .ToList();

    debugTable.Rows.Add("TodayTransDetail", $"Total transaction types today: {todayAllTransactions.Count}", "Number of different transaction types found today");

    foreach (var tranType in todayAllTransactions)
    {
        debugTable.Rows.Add("TodayTransDetail", $"{tranType.TranType}: {tranType.Count}", $"Today's {tranType.TranType} transaction count");
    }

    // Check if there are any transactions today at all
    var todayTotalTrans = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= today &&
                    pt.TranDate.Value < tomorrow)
        .Count();

    debugTable.Rows.Add("TodayTransDetail", $"Total transactions today: {todayTotalTrans}", "Total number of transactions today (all types)");

    // Check what today's date is being calculated as
    debugTable.Rows.Add("DateDebug", $"Today calculated as: {today:yyyy-MM-dd}", "Today's date as calculated by the system");
    debugTable.Rows.Add("DateDebug", $"Tomorrow calculated as: {tomorrow:yyyy-MM-dd}", "Tomorrow's date as calculated by the system");

    // Debug job material picking logic for today
    var todaySTKMTLCount = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType == "STK-MTL" &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= today &&
                    pt.TranDate.Value < tomorrow)
        .Count();

    debugTable.Rows.Add("JobMaterialDebug", $"Today STK-MTL transactions: {todaySTKMTLCount}", "Raw STK-MTL transaction count for today");

    var todaySTKMTLWithJobs = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType == "STK-MTL" &&
                    !string.IsNullOrEmpty(pt.JobNum) &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= today &&
                    pt.TranDate.Value < tomorrow)
        .Count();

    debugTable.Rows.Add("JobMaterialDebug", $"Today STK-MTL with JobNum: {todaySTKMTLWithJobs}", "STK-MTL transactions with job numbers");

    var todaySTKMTLWithJobProd = (from ptMtl in Db.PartTran
                                 join jh in Db.JobHead on new { ptMtl.Company, ptMtl.JobNum } equals new { jh.Company, jh.JobNum }
                                 join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                 where ptMtl.Company == companyID &&
                                       ptMtl.TranType == "STK-MTL" &&
                                       !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                       ptMtl.TranDate.HasValue &&
                                       ptMtl.TranDate.Value >= today &&
                                       ptMtl.TranDate.Value < tomorrow
                                 select ptMtl)
                                .Count();

    debugTable.Rows.Add("JobMaterialDebug", $"Today STK-MTL with JobProd: {todaySTKMTLWithJobProd}", "STK-MTL transactions that join to JobProd");

    var todaySTKMTLWithOrders = (from ptMtl in Db.PartTran
                                join jh in Db.JobHead on new { ptMtl.Company, ptMtl.JobNum } equals new { jh.Company, jh.JobNum }
                                join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                where ptMtl.Company == companyID &&
                                      ptMtl.TranType == "STK-MTL" &&
                                      !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                      ptMtl.TranDate.HasValue &&
                                      ptMtl.TranDate.Value >= today &&
                                      ptMtl.TranDate.Value < tomorrow &&
                                      jp.OrderNum > 0 &&
                                      jp.OrderLine > 0
                                select ptMtl)
                               .Count();

    debugTable.Rows.Add("JobMaterialDebug", $"Today STK-MTL with Orders: {todaySTKMTLWithOrders}", "STK-MTL transactions linked to sales orders");

    var todaySTKMTLWithJobMtl = (from ptMtl in Db.PartTran
                                join jm in Db.JobMtl on new { ptMtl.Company, ptMtl.JobNum, MtlSeq = ptMtl.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
                                join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
                                join jp in Db.JobProd on new { jh.Company, jh.JobNum } equals new { jp.Company, jp.JobNum }
                                where ptMtl.Company == companyID &&
                                      ptMtl.TranType == "STK-MTL" &&
                                      !string.IsNullOrEmpty(ptMtl.JobNum) &&
                                      ptMtl.TranDate.HasValue &&
                                      ptMtl.TranDate.Value >= today &&
                                      ptMtl.TranDate.Value < tomorrow &&
                                      jp.OrderNum > 0 &&
                                      jp.OrderLine > 0
                                select ptMtl)
                               .Count();

    debugTable.Rows.Add("JobMaterialDebug", $"Today STK-MTL with JobMtl: {todaySTKMTLWithJobMtl}", "STK-MTL transactions that join to JobMtl requirements");
    debugTable.Rows.Add("TransAnalysis", $"Today Regular Lines: {todaysRegularLines}", "Today's regular completed lines (non-kit)");
    debugTable.Rows.Add("TransAnalysis", $"Today Kit Components: {todaysKitComponents}", "Today's kit component lines (for completed orders)");
    debugTable.Rows.Add("TransAnalysis", $"Today Job Material Picks: {todaysJobMaterialPicks}", "Today's standalone job material picks");
    debugTable.Rows.Add("TransAnalysis", $"Today Total Lines: {todaysPickedLines}", "Today's total lines (regular + kit components + job materials)");
    debugTable.Rows.Add("TransAnalysis", $"Yesterday Regular Lines: {yesterdaysRegularLines}", "Yesterday's regular completed lines (non-kit)");
    debugTable.Rows.Add("TransAnalysis", $"Yesterday Kit Components: {yesterdaysKitComponents}", "Yesterday's kit component lines (for completed orders)");
    debugTable.Rows.Add("TransAnalysis", $"Yesterday Job Material Picks: {yesterdaysJobMaterialPicks}", "Yesterday's standalone job material picks");
    debugTable.Rows.Add("TransAnalysis", $"Yesterday Total Lines: {yesterdaysPickedLines}", "Yesterday's total lines (regular + kit components + job materials)");

    // Check transaction counts by type for today and yesterday
    var todayByType = Db.PartTran
        .Where(pt => pt.Company == companyID && pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= today && pt.TranDate.Value < tomorrow)
        .GroupBy(pt => pt.TranType)
        .Select(g => new { TranType = g.Key, Count = g.Select(pt => new { pt.OrderNum, pt.OrderLine }).Distinct().Count() })
        .ToList();

    var yesterdayByType = Db.PartTran
        .Where(pt => pt.Company == companyID && pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= yesterday && pt.TranDate.Value < today)
        .GroupBy(pt => pt.TranType)
        .Select(g => new { TranType = g.Key, Count = g.Select(pt => new { pt.OrderNum, pt.OrderLine }).Distinct().Count() })
        .ToList();

    foreach (var typeCount in todayByType.OrderByDescending(tc => tc.Count))
    {
        debugTable.Rows.Add("TodayByType", $"{typeCount.TranType}: {typeCount.Count}", $"Transaction type breakdown for today");
    }

    foreach (var typeCount in yesterdayByType.OrderByDescending(tc => tc.Count))
    {
        debugTable.Rows.Add("YesterdayByType", $"{typeCount.TranType}: {typeCount.Count}", $"Transaction type breakdown for yesterday");
    }

    result.Tables.Add(debugTable);
}
catch (Exception ex)
{
    // Set error message and return empty DataSet
    errorMessage = "Error: Failed to generate picked lines statistics - " + ex.Message;
    result = new DataSet("PickedLinesStatistics");
}
